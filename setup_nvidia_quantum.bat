@echo off
REM NVIDIA Quantum Computing Setup Script for the Multi-Agent AI System
REM This script automates the installation and integration of NVIDIA quantum technologies

echo ========================================
echo NVIDIA Quantum Computing Setup
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.10+ and try again
    pause
    exit /b 1
)

echo Python detected. Checking version...
python -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"
if errorlevel 1 (
    echo ERROR: Python 3.10+ is required
    echo Current version:
    python --version
    pause
    exit /b 1
)

echo ✓ Python version check passed
echo.

REM Check for NVIDIA GPU
echo Checking for NVIDIA GPU...
nvidia-smi >nul 2>&1
if errorlevel 1 (
    echo WARNING: nvidia-smi not found - no NVIDIA GPU detected
    echo The system will work in CPU-only mode
    echo.
) else (
    echo ✓ NVIDIA GPU detected
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits
    echo.
)

REM Step 1: Install NVIDIA quantum computing tools
echo Step 1: Installing NVIDIA quantum computing tools...
echo ================================================
echo.

echo Installing CUDA-Q...
python -m pip install cudaq
if errorlevel 1 (
    echo ERROR: Failed to install CUDA-Q
    pause
    exit /b 1
)
echo ✓ CUDA-Q installed successfully
echo.

echo Installing cuQuantum (this may take a while)...
REM Try conda first, then pip
conda --version >nul 2>&1
if not errorlevel 1 (
    echo Using conda to install cuQuantum...
    conda install -c conda-forge cuquantum-python -y
    if errorlevel 1 (
        echo Conda installation failed, trying pip...
        python -m pip install cuquantum-python
    )
) else (
    echo Conda not found, using pip...
    python -m pip install cuquantum-python
)

echo ✓ cuQuantum installation completed
echo.

echo Installing additional dependencies...
python -m pip install numpy scipy matplotlib jupyter qiskit cirq
echo ✓ Dependencies installed
echo.

REM Step 2: Run installation verification
echo Step 2: Verifying installation...
echo ===================================
echo.

python install_nvidia_quantum.py
if errorlevel 1 (
    echo ERROR: Installation verification failed
    pause
    exit /b 1
)

echo ✓ Installation verification completed
echo.

REM Step 3: Run integration with AI agent system
echo Step 3: Integrating with AI agent system...
echo ============================================
echo.

python integrate_nvidia_quantum.py
if errorlevel 1 (
    echo ERROR: Integration failed
    pause
    exit /b 1
)

echo ✓ Integration completed successfully
echo.

REM Step 4: Run tests
echo Step 4: Running integration tests...
echo ===================================
echo.

if exist test_nvidia_quantum.py (
    python test_nvidia_quantum.py
    if errorlevel 1 (
        echo WARNING: Some tests failed
        echo The system may still work, but with limited functionality
    ) else (
        echo ✓ All tests passed
    )
) else (
    echo Test script not found, skipping tests
)

echo.

REM Step 5: Create desktop shortcuts (optional)
echo Step 5: Creating shortcuts...
echo =============================
echo.

REM Create a shortcut to run the quantum dashboard
echo Creating quantum dashboard shortcut...
echo @echo off > start_quantum_dashboard.bat
echo echo Starting NVIDIA Quantum Dashboard... >> start_quantum_dashboard.bat
echo python run_unified_dashboard.py --quantum >> start_quantum_dashboard.bat
echo pause >> start_quantum_dashboard.bat

REM Create a shortcut to run quantum tests
echo Creating quantum test shortcut...
echo @echo off > test_quantum_system.bat
echo echo Testing NVIDIA Quantum System... >> test_quantum_system.bat
echo python test_nvidia_quantum.py >> test_quantum_system.bat
echo pause >> test_quantum_system.bat

echo ✓ Shortcuts created
echo.

REM Final summary
echo ========================================
echo NVIDIA Quantum Setup Complete!
echo ========================================
echo.
echo The following components have been installed and integrated:
echo ✓ CUDA-Q - Hybrid quantum-classical computing platform
echo ✓ cuQuantum - GPU-accelerated quantum simulations
echo ✓ Integration with AI agent system
echo ✓ Dashboard integration
echo ✓ Test suite
echo.
echo Available shortcuts:
echo - start_quantum_dashboard.bat - Launch quantum dashboard
echo - test_quantum_system.bat - Run quantum system tests
echo.
echo Configuration files:
echo - config/nvidia_quantum_config.json - Main configuration
echo - quantum_computing/ - Quantum computing modules
echo.
echo Next steps:
echo 1. Review the configuration in config/nvidia_quantum_config.json
echo 2. Run start_quantum_dashboard.bat to see the quantum dashboard
echo 3. Check the quantum_computing/README.md for usage examples
echo.
echo For support, check the logs in the logs/ directory
echo.

REM Check if we should start the dashboard
set /p start_dashboard="Would you like to start the quantum dashboard now? (y/n): "
if /i "%start_dashboard%"=="y" (
    echo.
    echo Starting quantum dashboard...
    start start_quantum_dashboard.bat
)

echo.
echo Setup completed successfully!
echo Press any key to exit...
pause >nul
