# NVIDIA Quantum Computing Integration

This directory contains the advanced quantum computing integration for the Multi-Agent AI System, featuring NVIDIA's cutting-edge quantum computing technologies including CUDA-Q, cuQuantum, and related quantum simulation libraries.

## Overview

This integration provides access to NVIDIA's quantum computing stack, enabling hybrid quantum-classical computing workflows, GPU-accelerated quantum simulations, and advanced quantum algorithm development. The system leverages NVIDIA's quantum technologies to enhance AI agent capabilities with quantum computational advantages.

## NVIDIA Quantum Technologies

### Core Components
- **CUDA-Q**: Hybrid quantum-classical computing platform
- **cuQuantum**: SDK for accelerating quantum circuit simulations
- **cuStateVec**: State vector simulation library
- **cuTensorNet**: Tensor network simulation library
- **cuDensityMat**: Density matrix simulation library

### Integration Components
- `nvidia_quantum_manager.py`: Main NVIDIA quantum technologies manager
- `cuda_q_connector.py`: CUDA-Q platform integration
- `cuquantum_simulator.py`: cuQuantum SDK integration
- `quantum_connector.py`: Enhanced quantum connector with NVIDIA support
- `quantum_algorithms.py`: Implementation of various quantum algorithms
- `quantum_simulator.py`: Local quantum simulator for testing

## Supported Quantum Providers

The quantum connector supports multiple quantum computing providers:

1. **NVIDIA CUDA-Q**: Hybrid quantum-classical computing platform
2. **NVIDIA cuQuantum**: GPU-accelerated quantum circuit simulation
3. **Simulator**: Local quantum simulator for testing and development
4. **IBM Quantum Experience**: Access to IBM's quantum computers
5. **Google Quantum AI**: Access to Google's quantum processors
6. **PennyLane**: Framework for quantum machine learning

## NVIDIA Quantum Algorithms

The integration supports various quantum algorithms powered by NVIDIA technologies:

### CUDA-Q Algorithms
1. **Variational Quantum Eigensolver (VQE)**: Hybrid quantum-classical optimization for finding ground states
2. **Quantum Approximate Optimization Algorithm (QAOA)**: Solving combinatorial optimization problems
3. **Quantum Fourier Transform**: Efficient implementation with GPU acceleration
4. **Grover's Search Algorithm**: Quadratic speedup for searching unsorted databases
5. **Quantum Neural Networks**: Hybrid quantum-classical machine learning models
6. **Hybrid Optimization**: Classical-quantum optimization workflows

### cuQuantum Algorithms
1. **State Vector Simulation**: GPU-accelerated state vector simulations up to 40+ qubits
2. **Tensor Network Simulation**: Efficient simulation of large quantum circuits
3. **Density Matrix Simulation**: Noisy quantum system simulations
4. **Random Circuit Sampling**: Quantum supremacy benchmarks
5. **Quantum Error Correction**: Fault-tolerant quantum computing simulations

### Classical Quantum Algorithms
1. **Shor's Factorization Algorithm**: Exponential speedup for factoring large numbers
2. **Quantum Phase Estimation**: Estimating eigenvalues of unitary operators
3. **Quantum Machine Learning**: Quantum-enhanced AI algorithms

## Installation

### Prerequisites
- Python 3.10+
- NVIDIA GPU with CUDA support (recommended)
- CUDA Toolkit 11.x or 12.x

### Quick Installation
```bash
# Install NVIDIA quantum computing tools
python install_nvidia_quantum.py

# Or install manually
pip install cudaq
conda install -c conda-forge cuquantum-python
```

### Verify Installation
```bash
python test_nvidia_quantum.py
```

## Usage

### Using NVIDIA Quantum Manager

```python
from quantum_computing.nvidia_quantum_manager import NVIDIAQuantumManager
import asyncio

async def run_nvidia_quantum():
    # Create NVIDIA quantum manager
    config = {
        "enabled": True,
        "cuda_q_enabled": True,
        "cuquantum_enabled": True,
        "gpu_acceleration": True
    }

    manager = NVIDIAQuantumManager(config)
    await manager.initialize()

    # Run VQE algorithm using CUDA-Q
    result = await manager.run_quantum_simulation(
        algorithm="vqe",
        parameters={
            "num_qubits": 4,
            "max_iterations": 100
        },
        backend="cuda_q"
    )

    print(f"VQE Result: {result}")

asyncio.run(run_nvidia_quantum())
```

### Using CUDA-Q Directly

```python
from quantum_computing.cuda_q_connector import CUDAQConnector
import asyncio

async def run_cuda_q_algorithm():
    # Create CUDA-Q connector
    config = {
        "enabled": True,
        "target": "nvidia",  # Use GPU acceleration
        "default_shots": 1024
    }

    connector = CUDAQConnector(config)
    await connector.initialize()

    # Run QAOA algorithm
    result = await connector.run_algorithm(
        algorithm="qaoa",
        parameters={
            "num_qubits": 6,
            "layers": 3
        }
    )

    print(f"QAOA Result: {result}")

asyncio.run(run_cuda_q_algorithm())
```

### Using cuQuantum for Large Simulations

```python
from quantum_computing.cuquantum_simulator import CuQuantumSimulator
import asyncio

async def run_cuquantum_simulation():
    # Create cuQuantum simulator
    config = {
        "enabled": True,
        "max_qubits": 40,
        "gpu_memory_limit": "auto"
    }

    simulator = CuQuantumSimulator(config)
    await simulator.initialize()

    # Run large-scale state vector simulation
    result = await simulator.simulate_circuit(
        algorithm="state_vector_simulation",
        parameters={
            "num_qubits": 30,
            "circuit_depth": 20,
            "shots": 1000
        }
    )

    print(f"Simulation Result: {result}")

asyncio.run(run_cuquantum_simulation())
```

### Enhanced Quantum Connector with NVIDIA Support

```python
from quantum_computing.quantum_connector import QuantumConnector
import asyncio

async def run_enhanced_quantum():
    # Create enhanced quantum connector with NVIDIA support
    quantum_config = {
        "provider": "nvidia",
        "enabled": True,
        "nvidia_quantum": True,
        "nvidia_config": {
            "cuda_q_enabled": True,
            "cuquantum_enabled": True
        }
    }

    connector = QuantumConnector(quantum_config)
    await connector.initialize()

    # Run quantum algorithm with automatic backend selection
    result = await connector.run_quantum_algorithm(
        algorithm="nvidia_quantum",
        parameters={
            "sub_algorithm": "vqe",
            "num_qubits": 8,
            "backend": "auto"  # Automatically select best backend
        }
    )

    print(f"Result: {result}")

asyncio.run(run_enhanced_quantum())
```

### Integration with Agents

Agents can use quantum computing for specialized tasks:

```python
# In an agent's execute_cycle method
async def execute_cycle(self):
    # Get quantum connector from services
    quantum_connector = self.get_service("quantum_connector")
    
    if quantum_connector:
        # Run a quantum algorithm
        result = await quantum_connector.run_quantum_algorithm(
            algorithm="shor_factorization",
            parameters={
                "number": 15
            }
        )
        
        # Use the result
        factors = result.get("factors", [])
        if factors:
            self.logger.info(f"Factorization result: {factors}")
```

## Local Quantum Simulator

For testing and development, a local quantum simulator is provided. This simulator can run basic quantum algorithms without requiring access to actual quantum hardware.

```python
from quantum_computing.quantum_simulator import QuantumSimulator

# Create simulator
simulator = QuantumSimulator()

# Run Grover's algorithm
result = simulator.run_grover_search(
    database_size=4,
    marked_item=2,
    shots=1024
)

print(f"Result: {result}")
```

## Quantum Machine Learning

The integration also supports quantum machine learning algorithms:

1. **Quantum Neural Networks**: Neural networks with quantum layers
2. **Quantum Support Vector Machines**: SVM with quantum kernels
3. **Quantum Generative Models**: Generative models using quantum circuits

Example:

```python
from quantum_computing.quantum_ml import QuantumNeuralNetwork
import numpy as np

# Create quantum neural network
qnn = QuantumNeuralNetwork(
    n_qubits=4,
    n_layers=2
)

# Train on data
X_train = np.random.rand(10, 4)
y_train = np.random.randint(0, 2, 10)
qnn.train(X_train, y_train, epochs=100)

# Make predictions
X_test = np.random.rand(5, 4)
predictions = qnn.predict(X_test)
```

## Configuration

Quantum computing can be configured in the `.env` file:

```
# Quantum computing settings
ENABLE_QUANTUM=True
QUANTUM_PROVIDER=simulator
QUANTUM_API_KEY=your_api_key
QUANTUM_USE_LOCAL=True
QUANTUM_LOCAL_URL=http://localhost:8081
```

## Requirements

To use the quantum computing integration, you may need to install additional packages:

```bash
# For simulator only
pip install numpy

# For IBM Quantum Experience
pip install qiskit

# For Google Quantum
pip install cirq

# For PennyLane
pip install pennylane

# For all providers
pip install numpy qiskit cirq pennylane
```
