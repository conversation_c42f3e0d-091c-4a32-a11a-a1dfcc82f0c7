"""
NVIDIA Quantum Computing Installation Script for the Multi-Agent AI System.

This script automates the installation of NVIDIA's quantum computing technologies
including CUDA-Q, cuQuantum, and related dependencies.
"""
import os
import sys
import subprocess
import platform
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NVIDIAQuantumInstaller:
    """Installer for NVIDIA quantum computing technologies."""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.architecture = platform.machine().lower()
        self.python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        
        # Installation status
        self.installation_status = {
            "cuda_q": False,
            "cuquantum": False,
            "custatevec": False,
            "cutensornet": False,
            "cudensitymat": False,
            "dependencies": False
        }
        
        logger.info(f"System: {self.system}, Architecture: {self.architecture}")
        logger.info(f"Python version: {self.python_version}")
    
    def check_prerequisites(self):
        """Check system prerequisites."""
        logger.info("Checking prerequisites...")
        
        # Check Python version
        if sys.version_info < (3, 10):
            logger.error("Python 3.10+ is required for NVIDIA quantum tools")
            return False
        
        # Check pip version
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                                  capture_output=True, text=True)
            pip_version = result.stdout.split()[1]
            logger.info(f"pip version: {pip_version}")
        except Exception as e:
            logger.error(f"Error checking pip version: {e}")
            return False
        
        # Check for CUDA (optional but recommended)
        try:
            result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("NVIDIA GPU detected")
            else:
                logger.warning("No NVIDIA GPU detected - CPU-only mode will be used")
        except FileNotFoundError:
            logger.warning("nvidia-smi not found - no NVIDIA drivers detected")
        
        return True
    
    def install_cuda_q(self):
        """Install NVIDIA CUDA-Q."""
        logger.info("Installing NVIDIA CUDA-Q...")
        
        try:
            # Install CUDA-Q using pip
            cmd = [sys.executable, "-m", "pip", "install", "cudaq"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("CUDA-Q installed successfully")
                self.installation_status["cuda_q"] = True
                return True
            else:
                logger.error(f"Failed to install CUDA-Q: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error installing CUDA-Q: {e}")
            return False
    
    def install_cuquantum(self):
        """Install NVIDIA cuQuantum."""
        logger.info("Installing NVIDIA cuQuantum...")
        
        try:
            # Check if conda is available
            conda_available = self._check_conda()
            
            if conda_available:
                # Install using conda (recommended)
                logger.info("Installing cuQuantum using conda...")
                cmd = ["conda", "install", "-c", "conda-forge", "cuquantum-python", "-y"]
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("cuQuantum installed successfully via conda")
                    self.installation_status["cuquantum"] = True
                    return True
                else:
                    logger.warning("Conda installation failed, trying pip...")
            
            # Fallback to pip installation
            logger.info("Installing cuQuantum using pip...")
            cmd = [sys.executable, "-m", "pip", "install", "cuquantum-python"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("cuQuantum installed successfully via pip")
                self.installation_status["cuquantum"] = True
                return True
            else:
                logger.error(f"Failed to install cuQuantum: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error installing cuQuantum: {e}")
            return False
    
    def install_dependencies(self):
        """Install additional dependencies."""
        logger.info("Installing additional dependencies...")
        
        dependencies = [
            "numpy>=1.21.0",
            "cupy-cuda12x",  # For CUDA 12.x
            "scipy",
            "matplotlib",
            "jupyter",
            "qiskit",  # Optional quantum framework
            "cirq",    # Optional quantum framework
        ]
        
        try:
            for dep in dependencies:
                logger.info(f"Installing {dep}...")
                cmd = [sys.executable, "-m", "pip", "install", dep]
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode != 0:
                    logger.warning(f"Failed to install {dep}: {result.stderr}")
                else:
                    logger.info(f"Successfully installed {dep}")
            
            self.installation_status["dependencies"] = True
            return True
            
        except Exception as e:
            logger.error(f"Error installing dependencies: {e}")
            return False
    
    def _check_conda(self):
        """Check if conda is available."""
        try:
            result = subprocess.run(["conda", "--version"], capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def verify_installation(self):
        """Verify that installations were successful."""
        logger.info("Verifying installations...")
        
        # Test CUDA-Q
        try:
            import cudaq
            logger.info("✓ CUDA-Q import successful")
            
            # Test basic functionality
            @cudaq.kernel
            def test_kernel():
                q = cudaq.qubit()
                cudaq.h(q)
            
            logger.info("✓ CUDA-Q basic functionality test passed")
            
        except ImportError:
            logger.error("✗ CUDA-Q import failed")
        except Exception as e:
            logger.error(f"✗ CUDA-Q functionality test failed: {e}")
        
        # Test cuQuantum
        try:
            import cuquantum
            logger.info("✓ cuQuantum import successful")
        except ImportError:
            logger.error("✗ cuQuantum import failed")
        
        # Test cuStateVec
        try:
            import custatevec
            logger.info("✓ cuStateVec import successful")
        except ImportError:
            logger.warning("⚠ cuStateVec not available (may require separate installation)")
        
        # Test cuTensorNet
        try:
            import cutensornet
            logger.info("✓ cuTensorNet import successful")
        except ImportError:
            logger.warning("⚠ cuTensorNet not available (may require separate installation)")
        
        # Test GPU availability
        try:
            import cupy
            gpu_count = cupy.cuda.runtime.getDeviceCount()
            logger.info(f"✓ {gpu_count} CUDA GPU(s) detected")
        except Exception:
            logger.warning("⚠ No CUDA GPUs detected - CPU-only mode")
    
    def create_test_script(self):
        """Create a test script to verify the installation."""
        test_script = """#!/usr/bin/env python3
\"\"\"
Test script for NVIDIA Quantum Computing installation.
\"\"\"
import sys

def test_cuda_q():
    \"\"\"Test CUDA-Q installation.\"\"\"
    try:
        import cudaq
        print("✓ CUDA-Q is available")
        
        # Test basic kernel
        @cudaq.kernel
        def bell_state():
            qubits = cudaq.qvector(2)
            cudaq.h(qubits[0])
            cudaq.cx(qubits[0], qubits[1])
        
        # Test sampling
        result = cudaq.sample(bell_state)
        print(f"✓ CUDA-Q sampling test passed: {len(result)} results")
        return True
        
    except ImportError:
        print("✗ CUDA-Q not available")
        return False
    except Exception as e:
        print(f"✗ CUDA-Q test failed: {e}")
        return False

def test_cuquantum():
    \"\"\"Test cuQuantum installation.\"\"\"
    try:
        import cuquantum
        print("✓ cuQuantum is available")
        return True
    except ImportError:
        print("✗ cuQuantum not available")
        return False

def test_gpu():
    \"\"\"Test GPU availability.\"\"\"
    try:
        import cupy
        gpu_count = cupy.cuda.runtime.getDeviceCount()
        print(f"✓ {gpu_count} CUDA GPU(s) available")
        return gpu_count > 0
    except Exception:
        print("⚠ No CUDA GPUs detected")
        return False

if __name__ == "__main__":
    print("NVIDIA Quantum Computing Installation Test")
    print("=" * 50)
    
    tests = [
        ("CUDA-Q", test_cuda_q),
        ("cuQuantum", test_cuquantum),
        ("GPU", test_gpu)
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"\\nTesting {name}...")
        if test_func():
            passed += 1
    
    print(f"\\nTest Results: {passed}/{len(tests)} passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! NVIDIA quantum tools are ready to use.")
    else:
        print("⚠ Some tests failed. Check the installation.")
"""
        
        test_file = Path("test_nvidia_quantum.py")
        with open(test_file, "w") as f:
            f.write(test_script)
        
        logger.info(f"Test script created: {test_file}")
        return test_file
    
    def run_installation(self):
        """Run the complete installation process."""
        logger.info("Starting NVIDIA Quantum Computing installation...")
        
        # Check prerequisites
        if not self.check_prerequisites():
            logger.error("Prerequisites check failed")
            return False
        
        # Install components
        success = True
        
        # Install CUDA-Q
        if not self.install_cuda_q():
            success = False
        
        # Install cuQuantum
        if not self.install_cuquantum():
            success = False
        
        # Install dependencies
        if not self.install_dependencies():
            success = False
        
        # Verify installation
        self.verify_installation()
        
        # Create test script
        test_file = self.create_test_script()
        
        # Print summary
        logger.info("\nInstallation Summary:")
        logger.info("=" * 50)
        for component, status in self.installation_status.items():
            status_str = "✓ Installed" if status else "✗ Failed"
            logger.info(f"{component}: {status_str}")
        
        if success:
            logger.info("\n🎉 NVIDIA Quantum Computing installation completed!")
            logger.info(f"Run 'python {test_file}' to test the installation.")
        else:
            logger.error("\n❌ Installation completed with errors.")
        
        return success

def main():
    """Main installation function."""
    installer = NVIDIAQuantumInstaller()
    return installer.run_installation()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
