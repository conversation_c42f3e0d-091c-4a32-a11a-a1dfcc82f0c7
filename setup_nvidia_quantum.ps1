# NVIDIA Quantum Computing Setup Script for the Multi-Agent AI System
# This PowerShell script automates the installation and integration of NVIDIA quantum technologies

param(
    [switch]$SkipTests,
    [switch]$NoInteractive,
    [string]$ConfigPath = "config/nvidia_quantum_config.json"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"

function Write-Header {
    param([string]$Text)
    Write-Host "========================================" -ForegroundColor $Cyan
    Write-Host $Text -ForegroundColor $Cyan
    Write-Host "========================================" -ForegroundColor $Cyan
    Write-Host ""
}

function Write-Success {
    param([string]$Text)
    Write-Host "✓ $Text" -ForegroundColor $Green
}

function Write-Error {
    param([string]$Text)
    Write-Host "✗ $Text" -ForegroundColor $Red
}

function Write-Warning {
    param([string]$Text)
    Write-Host "⚠ $Text" -ForegroundColor $Yellow
}

function Test-PythonVersion {
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "Python not found"
        }
        
        $versionMatch = $pythonVersion -match "Python (\d+)\.(\d+)"
        if ($versionMatch) {
            $major = [int]$matches[1]
            $minor = [int]$matches[2]
            
            if ($major -ge 3 -and $minor -ge 10) {
                Write-Success "Python version check passed: $pythonVersion"
                return $true
            } else {
                Write-Error "Python 3.10+ is required. Current: $pythonVersion"
                return $false
            }
        } else {
            Write-Error "Could not parse Python version"
            return $false
        }
    } catch {
        Write-Error "Python is not installed or not in PATH"
        return $false
    }
}

function Test-NvidiaGPU {
    try {
        $nvidiaInfo = nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "NVIDIA GPU detected:"
            Write-Host $nvidiaInfo -ForegroundColor $Cyan
            return $true
        } else {
            Write-Warning "nvidia-smi not found - no NVIDIA GPU detected"
            Write-Warning "The system will work in CPU-only mode"
            return $false
        }
    } catch {
        Write-Warning "No NVIDIA GPU detected - CPU-only mode"
        return $false
    }
}

function Install-CudaQ {
    Write-Host "Installing CUDA-Q..." -ForegroundColor $Cyan
    try {
        python -m pip install cudaq
        if ($LASTEXITCODE -eq 0) {
            Write-Success "CUDA-Q installed successfully"
            return $true
        } else {
            Write-Error "Failed to install CUDA-Q"
            return $false
        }
    } catch {
        Write-Error "Error installing CUDA-Q: $_"
        return $false
    }
}

function Install-CuQuantum {
    Write-Host "Installing cuQuantum (this may take a while)..." -ForegroundColor $Cyan
    
    # Try conda first
    try {
        conda --version | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Using conda to install cuQuantum..." -ForegroundColor $Cyan
            conda install -c conda-forge cuquantum-python -y
            if ($LASTEXITCODE -eq 0) {
                Write-Success "cuQuantum installed via conda"
                return $true
            } else {
                Write-Warning "Conda installation failed, trying pip..."
            }
        }
    } catch {
        Write-Host "Conda not found, using pip..." -ForegroundColor $Cyan
    }
    
    # Fallback to pip
    try {
        python -m pip install cuquantum-python
        if ($LASTEXITCODE -eq 0) {
            Write-Success "cuQuantum installed via pip"
            return $true
        } else {
            Write-Error "Failed to install cuQuantum"
            return $false
        }
    } catch {
        Write-Error "Error installing cuQuantum: $_"
        return $false
    }
}

function Install-Dependencies {
    Write-Host "Installing additional dependencies..." -ForegroundColor $Cyan
    
    $dependencies = @(
        "numpy>=1.21.0",
        "scipy",
        "matplotlib",
        "jupyter",
        "qiskit",
        "cirq"
    )
    
    foreach ($dep in $dependencies) {
        try {
            Write-Host "Installing $dep..." -ForegroundColor $Cyan
            python -m pip install $dep
            if ($LASTEXITCODE -eq 0) {
                Write-Success "$dep installed"
            } else {
                Write-Warning "Failed to install $dep"
            }
        } catch {
            Write-Warning "Error installing $dep: $_"
        }
    }
    
    Write-Success "Dependencies installation completed"
    return $true
}

function Run-InstallationVerification {
    Write-Host "Running installation verification..." -ForegroundColor $Cyan
    
    try {
        python install_nvidia_quantum.py
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Installation verification completed"
            return $true
        } else {
            Write-Error "Installation verification failed"
            return $false
        }
    } catch {
        Write-Error "Error during installation verification: $_"
        return $false
    }
}

function Run-SystemIntegration {
    Write-Host "Integrating with AI agent system..." -ForegroundColor $Cyan
    
    try {
        python integrate_nvidia_quantum.py
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Integration completed successfully"
            return $true
        } else {
            Write-Error "Integration failed"
            return $false
        }
    } catch {
        Write-Error "Error during integration: $_"
        return $false
    }
}

function Run-IntegrationTests {
    if ($SkipTests) {
        Write-Warning "Skipping tests as requested"
        return $true
    }
    
    Write-Host "Running integration tests..." -ForegroundColor $Cyan
    
    if (Test-Path "test_nvidia_quantum.py") {
        try {
            python test_nvidia_quantum.py
            if ($LASTEXITCODE -eq 0) {
                Write-Success "All tests passed"
                return $true
            } else {
                Write-Warning "Some tests failed"
                Write-Warning "The system may still work, but with limited functionality"
                return $false
            }
        } catch {
            Write-Warning "Error running tests: $_"
            return $false
        }
    } else {
        Write-Warning "Test script not found, skipping tests"
        return $true
    }
}

function Create-Shortcuts {
    Write-Host "Creating shortcuts..." -ForegroundColor $Cyan
    
    # Create quantum dashboard shortcut
    $dashboardScript = @"
@echo off
echo Starting NVIDIA Quantum Dashboard...
python run_unified_dashboard.py --quantum
pause
"@
    $dashboardScript | Out-File -FilePath "start_quantum_dashboard.bat" -Encoding ASCII
    
    # Create quantum test shortcut
    $testScript = @"
@echo off
echo Testing NVIDIA Quantum System...
python test_nvidia_quantum.py
pause
"@
    $testScript | Out-File -FilePath "test_quantum_system.bat" -Encoding ASCII
    
    # Create PowerShell quantum console
    $psScript = @"
# NVIDIA Quantum PowerShell Console
Write-Host "NVIDIA Quantum Computing Console" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Available commands:" -ForegroundColor Yellow
Write-Host "  python test_nvidia_quantum.py    - Run quantum tests"
Write-Host "  python integrate_nvidia_quantum.py - Re-run integration"
Write-Host "  python run_unified_dashboard.py --quantum - Start dashboard"
Write-Host ""
"@
    $psScript | Out-File -FilePath "quantum_console.ps1" -Encoding UTF8
    
    Write-Success "Shortcuts created"
    return $true
}

function Show-Summary {
    Write-Header "NVIDIA Quantum Setup Complete!"
    
    Write-Host "The following components have been installed and integrated:" -ForegroundColor $Green
    Write-Host "✓ CUDA-Q - Hybrid quantum-classical computing platform" -ForegroundColor $Green
    Write-Host "✓ cuQuantum - GPU-accelerated quantum simulations" -ForegroundColor $Green
    Write-Host "✓ Integration with AI agent system" -ForegroundColor $Green
    Write-Host "✓ Dashboard integration" -ForegroundColor $Green
    Write-Host "✓ Test suite" -ForegroundColor $Green
    Write-Host ""
    
    Write-Host "Available shortcuts:" -ForegroundColor $Yellow
    Write-Host "- start_quantum_dashboard.bat - Launch quantum dashboard"
    Write-Host "- test_quantum_system.bat - Run quantum system tests"
    Write-Host "- quantum_console.ps1 - PowerShell quantum console"
    Write-Host ""
    
    Write-Host "Configuration files:" -ForegroundColor $Yellow
    Write-Host "- config/nvidia_quantum_config.json - Main configuration"
    Write-Host "- quantum_computing/ - Quantum computing modules"
    Write-Host ""
    
    Write-Host "Next steps:" -ForegroundColor $Cyan
    Write-Host "1. Review the configuration in config/nvidia_quantum_config.json"
    Write-Host "2. Run start_quantum_dashboard.bat to see the quantum dashboard"
    Write-Host "3. Check the quantum_computing/README.md for usage examples"
    Write-Host ""
    
    Write-Host "For support, check the logs in the logs/ directory" -ForegroundColor $Yellow
}

# Main execution
try {
    Write-Header "NVIDIA Quantum Computing Setup"
    
    # Step 1: Check prerequisites
    Write-Host "Checking prerequisites..." -ForegroundColor $Cyan
    if (-not (Test-PythonVersion)) {
        exit 1
    }
    
    $hasGPU = Test-NvidiaGPU
    Write-Host ""
    
    # Step 2: Install NVIDIA quantum tools
    Write-Header "Step 1: Installing NVIDIA quantum computing tools"
    
    if (-not (Install-CudaQ)) {
        exit 1
    }
    
    if (-not (Install-CuQuantum)) {
        exit 1
    }
    
    if (-not (Install-Dependencies)) {
        exit 1
    }
    
    # Step 3: Verify installation
    Write-Header "Step 2: Verifying installation"
    if (-not (Run-InstallationVerification)) {
        exit 1
    }
    
    # Step 4: Integrate with AI system
    Write-Header "Step 3: Integrating with AI agent system"
    if (-not (Run-SystemIntegration)) {
        exit 1
    }
    
    # Step 5: Run tests
    Write-Header "Step 4: Running integration tests"
    Run-IntegrationTests | Out-Null
    
    # Step 6: Create shortcuts
    Write-Header "Step 5: Creating shortcuts"
    Create-Shortcuts | Out-Null
    
    # Show summary
    Show-Summary
    
    # Ask to start dashboard
    if (-not $NoInteractive) {
        $startDashboard = Read-Host "Would you like to start the quantum dashboard now? (y/n)"
        if ($startDashboard -eq "y" -or $startDashboard -eq "Y") {
            Write-Host ""
            Write-Host "Starting quantum dashboard..." -ForegroundColor $Cyan
            Start-Process "start_quantum_dashboard.bat"
        }
    }
    
    Write-Host ""
    Write-Success "Setup completed successfully!"
    
} catch {
    Write-Error "Setup failed: $_"
    Write-Host "Check the error messages above for details" -ForegroundColor $Red
    exit 1
}
